import click
from typing import Dict, Any
from src.data.yahoo_client import Yahoo<PERSON>lient
from src.engine.decision_engine import DecisionEngine
from src.cli.formatters import get_formatter

def fetch_and_analyze(symbol: str, period: str = '1y', detailed: bool = False, risk: str = 'moderate') -> Dict[str, Any]:
    """Fetch stock data and perform analysis"""
    # Initialize clients
    client = YahooClient()
    engine = DecisionEngine()
    
    # Validate and normalize symbol
    symbol = symbol.upper()
    if not client.is_valid_bse_symbol(symbol):
        raise ValueError(f"Invalid BSE symbol format: {symbol.replace('.BO', '')}")
    
    # Fetch stock data
    try:
        stock_data = client.fetch_stock_data(symbol, period)
        if not stock_data or stock_data.data.empty:
            raise ConnectionError(f"Failed to fetch data: No data available for {symbol}")
    except Exception as e:
        if 'network' in str(e).lower() or 'connection' in str(e).lower():
            raise ConnectionError(f"Failed to fetch data: Network error")
        raise
    
    # Perform analysis
    result = engine.analyze_stock(stock_data, risk_tolerance=risk)
    
    return result

def analyze_batch(symbols_file: str, **kwargs) -> list:
    """Analyze multiple stocks from file"""
    # TODO: Implement batch analysis
    raise NotImplementedError("Batch analysis not implemented yet")

@click.command()
@click.argument('symbol')
@click.option('--period', type=click.Choice(['1m', '3m', '6m', '1y', '2y']), default='1y', help='Analysis period')
@click.option('--risk', type=click.Choice(['conservative', 'moderate', 'aggressive']), default='moderate', help='Risk tolerance')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json', 'csv']), default='table', help='Output format')
@click.option('--detailed', is_flag=True, help='Show detailed technical analysis')
@click.option('--output', help='Output file path')
def analyze_stock(symbol, period, risk, output_format, detailed, output):
    """Analyze a BSE stock"""
    try:
        # Normalize symbol to uppercase
        symbol = symbol.upper()
        
        # Fetch and analyze stock
        result = fetch_and_analyze(symbol, period=period, detailed=detailed, risk=risk)
        
        # Format output
        formatter = get_formatter(output_format)
        if output_format == 'table':
            formatted_output = formatter.format_analysis(result, detailed=detailed)
        else:
            formatted_output = formatter.format_analysis(result)
        
        # Output to file or console
        if output:
            # Create directory if it doesn't exist
            import os
            os.makedirs(os.path.dirname(output), exist_ok=True)
            
            with open(output, 'w') as f:
                f.write(formatted_output)
            click.echo(f"Analysis saved to {output}")
        else:
            click.echo(formatted_output)
            
    except (ValueError, ConnectionError) as e:
        formatter = get_formatter(output_format)
        error_output = formatter.format_error(str(e))
        click.echo(error_output, err=True)
        raise click.ClickException(str(e))
    except Exception as e:
        formatter = get_formatter(output_format)
        error_output = formatter.format_error(f"Unexpected error occurred: {str(e)}")
        click.echo(error_output, err=True)
        raise click.ClickException(f"Unexpected error occurred: {str(e)}")

@click.command()
@click.option('--input', 'input_file', required=True, help='Input file with stock symbols')
@click.option('--output', help='Output directory')
@click.option('--format', 'output_format', type=click.Choice(['table', 'json', 'csv']), default='csv', help='Output format')
def batch(input_file, output, output_format):
    """Batch analyze multiple stocks"""
    # TODO: Implement batch command
    raise NotImplementedError("Batch command not implemented yet")

@click.group()
@click.version_option(version='0.1.0')
def cli():
    """BSE Stock Analysis Tool"""
    pass

# Add commands to CLI group
cli.add_command(analyze_stock, name='analyze')
cli.add_command(batch)