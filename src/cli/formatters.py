import json
from datetime import datetime
from typing import Dict, Any, List
from abc import ABC, abstractmethod

def format_currency(amount: float) -> str:
    """Format amount as Indian Rupee currency"""
    def indian_format(num):
        """Format number with Indian numbering system"""
        if num == 0:
            return "0.00"
        
        # Convert to string and split at decimal
        num_str = f"{num:.2f}"
        integer_part, decimal_part = num_str.split('.')
        
        # Add commas in Indian style (last 3 digits, then every 2)
        if len(integer_part) <= 3:
            return f"{integer_part}.{decimal_part}"
        
        # Reverse for easier processing
        reversed_digits = integer_part[::-1]
        formatted = ""
        
        # First group of 3 digits
        formatted += reversed_digits[:3]
        remaining = reversed_digits[3:]
        
        # Then groups of 2
        i = 0
        while i < len(remaining):
            if formatted:
                formatted += ","
            formatted += remaining[i:i+2]
            i += 2
        
        # Reverse back
        formatted = formatted[::-1]
        return f"{formatted}.{decimal_part}"
    
    if amount < 0:
        return f"-₹{indian_format(abs(amount))}"
    return f"₹{indian_format(amount)}"

def format_percentage(value: float, precision: int = 1) -> str:
    """Format decimal as percentage"""
    return f"{value * 100:.{precision}f}%"

class BaseFormatter(ABC):
    """Base class for output formatters"""
    
    @abstractmethod
    def format_analysis(self, result: Dict[str, Any], **kwargs) -> str:
        """Format analysis result"""
        pass
    
    @abstractmethod
    def format_error(self, error_message: str) -> str:
        """Format error message"""
        pass

class TerminalFormatter(BaseFormatter):
    """Format output for terminal display"""
    
    def format_analysis(self, result: Dict[str, Any], detailed: bool = False) -> str:
        """Format analysis result for terminal"""
        symbol = result.get('symbol', 'N/A')
        recommendation = result.get('recommendation', 'HOLD')
        confidence = result.get('confidence', 0.0)
        current_price = result.get('current_price', 0.0)
        
        # Basic formatting with required elements
        output = []
        output.append(f"BSE Stock Analysis: {symbol}")
        output.append("═" * 50)
        output.append("")
        
        # Price line
        price_str = format_currency(current_price) if current_price else "N/A"
        output.append(f"Current Price: {price_str}")
        output.append("")
        
        # Recommendation box  
        confidence_pct = format_percentage(confidence, precision=0) if confidence else "N/A"
        output.append("┌─ RECOMMENDATION " + "─" * 32 + "┐")
        output.append(f"│ {recommendation} (Confidence: {confidence_pct})" + " " * (48 - len(f"{recommendation} (Confidence: {confidence_pct})")) + "│")
        output.append("└" + "─" * 49 + "┘")
        output.append("")
        
        # Key metrics section
        output.append("┌─ KEY METRICS " + "─" * 35 + "┐")
        
        # Add technical indicators if available
        sma_20 = result.get('sma_20')
        rsi = result.get('rsi')
        if sma_20:
            output.append(f"│ 20-day SMA: {format_currency(sma_20)}" + " " * (48 - len(f"20-day SMA: {format_currency(sma_20)}")) + "│")
        if rsi:
            output.append(f"│ RSI (14): {rsi:.1f}" + " " * (48 - len(f"RSI (14): {rsi:.1f}")) + "│")
        
        # Add Bollinger Band indicators (always show if available)
        bb_status = result.get('bb_status', 'unknown')
        bollinger_percent_b = result.get('bollinger_percent_b')
        bollinger_squeeze = result.get('bollinger_squeeze', {})
        
        if bb_status == 'available' and bollinger_percent_b is not None:
            percent_b_display = f"{bollinger_percent_b:.2f}"
            if bollinger_percent_b > 0.8:
                percent_b_display += " (Overbought)"
            elif bollinger_percent_b < 0.2:
                percent_b_display += " (Oversold)"
            output.append(f"│ %B Position: {percent_b_display}" + " " * (48 - len(f"%B Position: {percent_b_display}")) + "│")
            
            # Show squeeze status
            is_squeeze = bollinger_squeeze.get('is_squeeze', False)
            if is_squeeze:
                intensity = bollinger_squeeze.get('squeeze_intensity', 0)
                squeeze_text = f"Active ({intensity:.1f})" if intensity else "Active"
                output.append(f"│ BB Squeeze: {squeeze_text}" + " " * (48 - len(f"BB Squeeze: {squeeze_text}")) + "│")
        elif 'insufficient_data' in bb_status:
            output.append(f"│ BB Analysis: {bb_status}" + " " * (48 - len(f"BB Analysis: {bb_status}")) + "│")
        
        # Add detailed information if requested
        if detailed:
            bb_upper = result.get('bb_upper')
            relative_volume = result.get('relative_volume')
            if bb_upper:
                output.append(f"│ Bollinger Upper: {format_currency(bb_upper)}" + " " * (48 - len(f"Bollinger Upper: {format_currency(bb_upper)}")) + "│")
            if relative_volume:
                output.append(f"│ Relative Volume: {relative_volume:.1f}" + " " * (48 - len(f"Relative Volume: {relative_volume:.1f}")) + "│")
            
            # Show breakout information in detailed mode
            bollinger_breakout = result.get('bollinger_breakout', {})
            if bb_status == 'available' and bollinger_breakout:
                upper_breakout = bollinger_breakout.get('upper_breakout', False)
                lower_breakout = bollinger_breakout.get('lower_breakout', False)
                if upper_breakout or lower_breakout:
                    breakout_type = "Upper" if upper_breakout else "Lower"
                    strength = bollinger_breakout.get('breakout_strength', 0)
                    breakout_text = f"{breakout_type} ({strength:.2f})" if strength else breakout_type
                    output.append(f"│ BB Breakout: {breakout_text}" + " " * (48 - len(f"BB Breakout: {breakout_text}")) + "│")
        
        # Handle missing data
        if not sma_20 and not rsi:
            output.append("│ Technical Indicators: N/A" + " " * 22 + "│")
        
        output.append("└" + "─" * 49 + "┘")
        
        return "\n".join(output)
    
    def format_error(self, error_message: str) -> str:
        """Format error message for terminal"""
        return f"Error: {error_message}"

class JSONFormatter(BaseFormatter):
    """Format output as JSON"""
    
    def format_analysis(self, result: Dict[str, Any], pretty: bool = False) -> str:
        """Format analysis result as JSON"""
        # Create structured JSON output
        json_output = {
            'symbol': result.get('symbol', 'N/A'),
            'timestamp': datetime.now().isoformat() + 'Z',
            'current_price': result.get('current_price', 0.0),
            'recommendation': {
                'action': result.get('recommendation', 'HOLD'),
                'confidence': result.get('confidence', 0.0),
                'target_price': result.get('target_price', 0.0),
                'stop_loss': result.get('stop_loss', 0.0),
                'risk_level': result.get('risk_level', 'MEDIUM')
            },
            'technical_indicators': {
                'sma_20': result.get('sma_20'),
                'sma_50': result.get('sma_50'),
                'sma_200': result.get('sma_200'),
                'rsi': result.get('rsi'),
                'macd': {
                    'line': result.get('macd_line'),
                    'signal': result.get('macd_signal'),
                    'histogram': result.get('macd_histogram'),
                    'crossover': result.get('macd_crossover')
                },
                'bollinger_bands': {
                    'upper': result.get('bb_upper'),
                    'middle': result.get('bb_middle'),
                    'lower': result.get('bb_lower')
                }
            },
            'volume_analysis': {
                'current_volume': result.get('current_volume', 0),
                'volume_sma': result.get('volume_sma'),
                'relative_volume': result.get('relative_volume')
            },
            'signals': {
                'trend_signal': result.get('trend_signal', 'neutral'),
                'momentum_signal': result.get('momentum_signal', 'neutral')
            }
        }
        
        if pretty:
            return json.dumps(json_output, indent=2, default=str)
        return json.dumps(json_output, default=str)
    
    def format_error(self, error_message: str) -> str:
        """Format error message as JSON"""
        error_output = {
            'error': error_message,
            'timestamp': datetime.now().isoformat() + 'Z'
        }
        return json.dumps(error_output)

class CSVFormatter(BaseFormatter):
    """Format output as CSV"""
    
    def format_analysis(self, result: Dict[str, Any]) -> str:
        """Format analysis result as CSV"""
        # Define CSV headers
        headers = [
            'symbol', 'current_price', 'recommendation', 'confidence',
            'sma_20', 'sma_50', 'rsi', 'target_price', 'stop_loss'
        ]
        
        # Create data row
        data = [
            result.get('symbol', 'N/A'),
            result.get('current_price', 0.0),
            result.get('recommendation', 'HOLD'),
            result.get('confidence', 0.0),
            result.get('sma_20', ''),
            result.get('sma_50', ''),
            result.get('rsi', ''),
            result.get('target_price', ''),
            result.get('stop_loss', '')
        ]
        
        # Convert to strings and handle special characters
        data_str = []
        for item in data:
            if item is None or item == '':
                data_str.append('')
            elif isinstance(item, str) and (',' in item or '"' in item):
                # Escape quotes and wrap in quotes
                escaped = item.replace('"', '""')
                data_str.append(f'"{escaped}"')
            else:
                data_str.append(str(item))
        
        # Return CSV format
        header_line = ','.join(headers)
        data_line = ','.join(data_str)
        return f"{header_line}\n{data_line}"
    
    def format_batch(self, results: List[Dict[str, Any]]) -> str:
        """Format batch results as CSV"""
        if not results:
            return "symbol,current_price,recommendation"
        
        # Use the first result to get headers, then format all results
        lines = []
        for i, result in enumerate(results):
            formatted = self.format_analysis(result)
            if i == 0:
                lines.append(formatted)  # Include header for first result
            else:
                lines.append(formatted.split('\n')[1])  # Only data line for subsequent results
        
        return '\n'.join(lines)
    
    def format_error(self, error_message: str) -> str:
        """Format error message as CSV"""
        return f"error\n{error_message}"

def get_formatter(format_type: str) -> BaseFormatter:
    """Factory function to get formatter by type"""
    formatters = {
        'table': TerminalFormatter,
        'json': JSONFormatter, 
        'csv': CSVFormatter
    }
    
    if format_type not in formatters:
        raise ValueError(f"Unsupported format: {format_type}")
    
    return formatters[format_type]()