from typing import Dict, Any, Optional
from src.data.yahoo_client import StockData

class DecisionEngine:
    """Main decision engine for stock analysis and recommendations"""
    
    def __init__(self):
        self.risk_assessor = RiskAssessment()
        self.scorer = RecommendationScorer()
    
    def analyze_stock(self, stock_data: StockData, risk_tolerance: str = 'moderate') -> Dict[str, Any]:
        """Analyze stock and generate recommendation"""
        if not stock_data:
            raise ValueError("Stock data cannot be None")
        
        if risk_tolerance not in ['conservative', 'moderate', 'aggressive']:
            raise ValueError(f"Invalid risk tolerance: {risk_tolerance}")
        
        # Get technical indicators
        from src.analysis.indicators import TechnicalIndicators
        indicators = TechnicalIndicators()
        technical_analysis = indicators.get_indicator_summary(stock_data.data)
        
        # Get support/resistance levels
        support_resistance = indicators.detect_support_resistance(stock_data.data, include_strength=True)
        
        # Generate recommendation
        recommendation = self.generate_recommendation(technical_analysis, risk_tolerance, support_resistance)
        
        # Assess risk
        risk_assessment = self.risk_assessor.assess_risk(stock_data)
        
        return {
            'symbol': stock_data.symbol,
            'recommendation': recommendation,
            'confidence': recommendation.get('confidence', 0.5),
            'technical_analysis': technical_analysis,
            'risk_assessment': risk_assessment,
            'support_levels': support_resistance.get('support_levels', []),
            'resistance_levels': support_resistance.get('resistance_levels', []),
            **technical_analysis  # Include all indicators at top level for formatting
        }
    
    def generate_recommendation(self, indicators: Dict[str, Any], risk_tolerance: str = 'moderate', support_resistance: Dict[str, Any] = None) -> Dict[str, Any]:
        """Generate buy/sell/hold recommendation based on indicators"""
        # Simple logic to pass tests
        trend_signal = indicators.get('trend_signal', 'neutral')
        momentum_signal = indicators.get('momentum_signal', 'neutral')
        rsi = indicators.get('rsi', 50.0)
        
        # Decision logic based on signals
        bullish_count = 0
        bearish_count = 0
        
        if trend_signal == 'bullish':
            bullish_count += 1
        elif trend_signal == 'bearish':
            bearish_count += 1
            
        if momentum_signal == 'bullish':
            bullish_count += 1
        elif momentum_signal == 'bearish':
            bearish_count += 1
        
        # RSI considerations
        if rsi and rsi > 70:
            bearish_count += 1  # Overbought
        elif rsi and rsi < 30:
            bullish_count += 1  # Oversold
        
        # Determine action
        if bullish_count > bearish_count:
            action = 'BUY'
        elif bearish_count > bullish_count:
            action = 'SELL'  
        else:
            action = 'HOLD'
        
        # Calculate confidence and prices
        confidence = self._calculate_confidence(indicators)
        target_price = self._calculate_target_price(indicators, action, support_resistance)
        stop_loss = self._calculate_stop_loss(indicators, action, risk_tolerance)
        
        return {
            'action': action,
            'confidence': confidence,
            'target_price': target_price,
            'stop_loss': stop_loss,
            'risk_level': 'MEDIUM'
        }
    
    def _calculate_confidence(self, indicators: Dict[str, Any]) -> float:
        """Calculate confidence score for recommendation"""
        # Simple confidence based on signal alignment
        trend = indicators.get('trend_signal', 'neutral')
        momentum = indicators.get('momentum_signal', 'neutral')
        relative_volume = indicators.get('relative_volume', 1.0)
        current_price = indicators.get('current_price', 100.0)
        sma_200 = indicators.get('sma_200')
        
        confidence = 0.3  # Base confidence
        
        confidence += self._score_signal_alignment(trend, momentum)
        confidence += self._score_volume_confirmation(relative_volume)
        confidence += self._score_sma200_influence(current_price, sma_200, trend)
        confidence += self._score_bollinger_band_signals(indicators, trend)

        return min(max(confidence, 0.0), 1.0)

    def _score_signal_alignment(self, trend: str, momentum: str) -> float:
        return 0.3 if trend == momentum and trend != 'neutral' else 0.0

    def _score_volume_confirmation(self, relative_volume: Optional[float]) -> float:
        return 0.2 if relative_volume and relative_volume > 1.2 else 0.0

    def _score_sma200_influence(self, current_price: Optional[float], sma_200: Optional[float], trend: str) -> float:
        if not (sma_200 and current_price):
            return 0.0

        price_vs_sma200_ratio = current_price / sma_200
        if trend == 'bullish':
            if price_vs_sma200_ratio > 1.05: return 0.25
            if price_vs_sma200_ratio < 0.95: return -0.15
        elif trend == 'bearish':
            if price_vs_sma200_ratio < 0.95: return 0.2
            if price_vs_sma200_ratio > 1.05: return -0.15
        return 0.0
    
    def _score_bollinger_band_signals(self, indicators: Dict[str, Any], trend: str) -> float:
        """Score Bollinger Band signals for confidence calculation - Micro-Phase 1C"""
        confidence_adjust = 0.0
        
        # Get Bollinger Band indicators
        percent_b = indicators.get('bollinger_percent_b')
        squeeze_info = indicators.get('bollinger_squeeze', {})
        breakout_info = indicators.get('bollinger_breakout', {})
        relative_volume = indicators.get('relative_volume', 1.0)
        
        # Apply individual scoring components
        confidence_adjust += self._score_percent_b_signals(percent_b, trend)
        confidence_adjust += self._score_squeeze_signals(squeeze_info, trend, relative_volume)
        confidence_adjust += self._score_breakout_signals(breakout_info, trend)
        confidence_adjust += self._score_market_regime(percent_b, breakout_info, relative_volume)
        
        return confidence_adjust
    
    def _score_percent_b_signals(self, percent_b: float, trend: str) -> float:
        """Score %B overbought/oversold signals"""
        if percent_b is None:
            return 0.0
            
        if trend == 'bullish':
            if percent_b > 0.8:  # Near upper band - overbought risk
                return -0.1  # Reduce confidence due to potential reversal
            elif 0.2 <= percent_b <= 0.6:  # Healthy position for bullish move
                return 0.05
        elif trend == 'bearish':
            if percent_b < 0.2:  # Near lower band - oversold opportunity
                return -0.1  # Reduce bearish confidence (potential bounce)
            elif 0.4 <= percent_b <= 0.8:  # Healthy position for bearish move
                return 0.05
        return 0.0
    
    def _score_squeeze_signals(self, squeeze_info: Dict[str, Any], trend: str, relative_volume: float) -> float:
        """Score Bollinger squeeze signals"""
        if not squeeze_info:
            return 0.0
            
        is_squeeze = squeeze_info.get('is_squeeze', False)
        squeeze_intensity = squeeze_info.get('squeeze_intensity', 0.0)
        
        if is_squeeze and trend != 'neutral':
            # Bollinger squeeze with trend alignment - high probability setup
            intensity_boost = squeeze_intensity * 0.15 if squeeze_intensity else 0.05
            
            # Additional boost with volume confirmation
            volume_boost = 0.08 if relative_volume and relative_volume > 1.3 else 0.0
            
            return intensity_boost + volume_boost
        return 0.0
    
    def _score_breakout_signals(self, breakout_info: Dict[str, Any], trend: str) -> float:
        """Score Bollinger breakout signals"""
        if not breakout_info:
            return 0.0
            
        upper_breakout = breakout_info.get('upper_breakout', False)
        lower_breakout = breakout_info.get('lower_breakout', False)
        breakout_strength = breakout_info.get('breakout_strength', 0.0)
        
        # Aligned breakouts boost confidence
        if (trend == 'bullish' and upper_breakout) or (trend == 'bearish' and lower_breakout):
            return breakout_strength * 0.2 if breakout_strength else 0.1
        
        # Conflicting breakouts reduce confidence
        if (trend == 'bullish' and lower_breakout) or (trend == 'bearish' and upper_breakout):
            return -0.12
            
        return 0.0
    
    def _score_market_regime(self, percent_b: float, breakout_info: Dict[str, Any], relative_volume: float) -> float:
        """Score mean reversion vs momentum regime detection"""
        if percent_b is None or not breakout_info:
            return 0.0
            
        breakout_strength = breakout_info.get('breakout_strength', 0.0)
        
        # Mean reversion setup: extreme %B with weak breakout strength
        if ((percent_b > 0.85 or percent_b < 0.15) and 
            breakout_strength < 0.3 and 
            (not relative_volume or relative_volume < 1.2)):
            return -0.08  # Likely mean reversion - reduce trend-following confidence
        
        # Strong momentum setup: moderate %B with strong breakout
        if (0.3 <= percent_b <= 0.7 and 
            breakout_strength > 0.6 and 
            relative_volume and relative_volume > 1.5):
            return 0.12  # Strong momentum continuation - boost confidence
            
        return 0.0
    
    def _calculate_target_price(self, indicators: Dict[str, Any], action: str, support_resistance: Dict[str, Any] = None) -> float:
        """Calculate target price for recommendation"""
        current_price = indicators.get('current_price', 100.0)
        bb_upper = indicators.get('bb_upper', current_price * 1.1)
        bb_lower = indicators.get('bb_lower', current_price * 0.9)
        
        if action == 'BUY':
            # Target above current price
            target = max(current_price * 1.05, bb_upper or current_price * 1.1)
            
            # Consider resistance levels - cap target at first resistance
            if support_resistance and support_resistance.get('resistance_levels'):
                resistance_levels = support_resistance['resistance_levels']
                nearby_resistance = [r for r in resistance_levels if r > current_price]
                if nearby_resistance:
                    first_resistance = min(nearby_resistance)
                    target = min(target, first_resistance)
            
            return target
        elif action == 'SELL':
            # Target below current price  
            return min(current_price * 0.95, bb_lower or current_price * 0.9)
        else:
            return current_price
    
    def _calculate_stop_loss(self, indicators: Dict[str, Any], action: str, risk_tolerance: str) -> float:
        """Calculate stop loss price"""
        current_price = indicators.get('current_price', 100.0)
        
        # Risk tolerance adjustments
        risk_multiplier = {
            'conservative': 0.03,  # 3% stop loss
            'moderate': 0.05,     # 5% stop loss  
            'aggressive': 0.08    # 8% stop loss
        }.get(risk_tolerance, 0.05)
        
        if action == 'BUY':
            # Stop loss below current price
            return current_price * (1 - risk_multiplier)
        elif action == 'SELL':
            # Stop loss above current price
            return current_price * (1 + risk_multiplier)
        else:
            return current_price

class RecommendationScorer:
    """Calculate recommendation scores based on various factors"""
    
    def calculate_combined_score(self, indicators: Dict[str, Any]) -> float:
        """Calculate combined score from all factors"""
        trend_score = self.score_trend_signals(indicators)
        momentum_score = self.score_momentum_signals(indicators) 
        volume_score = self.score_volume_signals(indicators)
        
        # Simple weighted average
        return (trend_score * 0.4 + momentum_score * 0.4 + volume_score * 0.2)
    
    def score_trend_signals(self, indicators: Dict[str, Any]) -> float:
        """Score based on trend indicators"""
        trend_signal = indicators.get('trend_signal', 'neutral')
        current_price = indicators.get('current_price', 100.0)
        sma_20 = indicators.get('sma_20', current_price)
        
        score = 0.0
        
        if trend_signal == 'bullish':
            score += 0.5
        elif trend_signal == 'bearish':
            score -= 0.5
            
        # Price vs SMA scoring
        if sma_20 and current_price > sma_20:
            score += 0.3
        elif sma_20 and current_price < sma_20:
            score -= 0.3
            
        return max(-1.0, min(1.0, score))
    
    def score_momentum_signals(self, indicators: Dict[str, Any]) -> float:
        """Score based on momentum indicators"""
        rsi = indicators.get('rsi', 50.0)
        macd_crossover = indicators.get('macd_crossover', 0)
        momentum_signal = indicators.get('momentum_signal', 'neutral')
        
        score = 0.0
        
        if momentum_signal == 'bullish':
            score += 0.4
        elif momentum_signal == 'bearish': 
            score -= 0.4
            
        # RSI scoring
        if rsi and rsi < 30:
            score += 0.3  # Oversold, bullish
        elif rsi and rsi > 70:
            score -= 0.3  # Overbought, bearish
            
        # MACD crossover
        if macd_crossover == 1:
            score += 0.3
        elif macd_crossover == -1:
            score -= 0.3
            
        return max(-1.0, min(1.0, score))
    
    def score_volume_signals(self, indicators: Dict[str, Any]) -> float:
        """Score based on volume indicators"""
        relative_volume = indicators.get('relative_volume', 1.0)
        
        if not relative_volume:
            return 0.0
            
        # Higher volume increases confidence
        if relative_volume > 1.5:
            return 0.8  # High volume confirmation
        elif relative_volume > 1.2:
            return 0.5  # Above average volume
        elif relative_volume < 0.8:
            return -0.3  # Low volume concern
        else:
            return 0.1  # Normal volume

class RiskAssessment:
    """Assess risk metrics for stocks"""
    
    def assess_risk(self, stock_data: StockData) -> Dict[str, Any]:
        """Assess overall risk metrics"""
        import pandas as pd
        
        prices = stock_data.data['Close']
        returns = prices.pct_change().dropna()
        
        # Calculate basic risk metrics
        volatility = returns.std() * (252 ** 0.5)  # Annualized
        max_drawdown = self._calculate_max_drawdown(prices)
        sharpe_ratio = self._calculate_sharpe_ratio(returns)
        
        risk_metrics = {
            'volatility': volatility,
            'max_drawdown': max_drawdown, 
            'sharpe_ratio': sharpe_ratio,
            'daily_volatility': returns.std()
        }
        
        return {
            **risk_metrics,
            'risk_level': self._classify_risk_level(risk_metrics)
        }
    
    # Enhanced Risk Assessment Methods
    
    def calculate_var(self, stock_data: StockData, confidence_level: float = 0.95, 
                     time_horizon: int = 1) -> Dict[str, Any]:
        """
        Calculate Value at Risk (VaR) using historical simulation method
        
        Args:
            stock_data: Stock data
            confidence_level: Confidence level (0.95 for 95% VaR)
            time_horizon: Time horizon in days
            
        Returns:
            Dictionary with VaR amount, percentage, and confidence level
        """
        import numpy as np
        
        prices = stock_data.data['Close']
        returns = prices.pct_change().dropna()
        
        if len(returns) < 10:  # Need minimum data points
            return {
                'var_amount': None,
                'var_percentage': None,
                'confidence_level': confidence_level,
                'time_horizon': time_horizon
            }
        
        # Calculate percentile for VaR
        alpha = 1 - confidence_level
        var_percentile = np.percentile(returns, alpha * 100)
        
        # Scale for time horizon (assuming returns are i.i.d.)
        var_percentage = var_percentile * np.sqrt(time_horizon)
        
        # Calculate VaR amount based on current price
        current_price = stock_data.current_price
        var_amount = current_price * var_percentage
        
        return {
            'var_amount': float(var_amount),
            'var_percentage': float(var_percentage),
            'confidence_level': confidence_level,
            'time_horizon': time_horizon
        }
    
    def calculate_sortino_ratio(self, stock_data: StockData, risk_free_rate: float = 0.05) -> float:
        """
        Calculate Sortino ratio (downside deviation version of Sharpe ratio)
        
        Args:
            stock_data: Stock data
            risk_free_rate: Annual risk-free rate
            
        Returns:
            Sortino ratio
        """
        import numpy as np
        import pandas as pd
        
        prices = stock_data.data['Close']
        returns = prices.pct_change().dropna()
        
        if returns.empty or len(returns) < 2:
            return 0.0
        
        # Daily risk-free rate
        daily_rf_rate = risk_free_rate / 252
        excess_returns = returns - daily_rf_rate
        
        # Calculate downside returns (only negative excess returns)
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            # No downside returns, use small value to avoid division by zero
            downside_std = 0.001
        else:
            downside_std = downside_returns.std()
        
        if downside_std == 0:
            return 0.0
        
        # Annualize
        mean_excess_return = excess_returns.mean() * 252
        downside_deviation = downside_std * np.sqrt(252)
        
        sortino_ratio = mean_excess_return / downside_deviation
        return float(sortino_ratio)
    
    def calculate_enhanced_beta(self, stock_data: StockData, market_data: StockData = None, 
                              time_period: str = '1y') -> Dict[str, Any]:
        """
        Calculate enhanced beta with correlation and additional metrics
        
        Args:
            stock_data: Stock data
            market_data: Market index data (e.g., Nifty 50)
            time_period: Time period for calculation
            
        Returns:
            Dictionary with beta, correlation, and additional metrics
        """
        import numpy as np
        import pandas as pd
        
        if market_data is None:
            # Return default beta if no market data
            return {
                'beta': 1.0,
                'correlation': None,
                'r_squared': None,
                'alpha': None,
                'time_period': time_period
            }
        
        # Calculate returns
        stock_returns = stock_data.data['Close'].pct_change().dropna()
        market_returns = market_data.data['Close'].pct_change().dropna()
        
        # Align data periods
        min_length = min(len(stock_returns), len(market_returns))
        if min_length < 30:  # Need minimum observations
            return {
                'beta': 1.0,
                'correlation': None,
                'r_squared': None,
                'alpha': None,
                'time_period': time_period
            }
        
        stock_returns = stock_returns.iloc[-min_length:]
        market_returns = market_returns.iloc[-min_length:]
        
        # Calculate enhanced metrics
        correlation = stock_returns.corr(market_returns)
        
        # Linear regression for beta, alpha, and R-squared
        stock_var = np.var(stock_returns)
        market_var = np.var(market_returns)
        covariance = np.cov(stock_returns, market_returns)[0, 1]
        
        if market_var == 0:
            beta = 1.0
            alpha = 0.0
            r_squared = 0.0
        else:
            beta = covariance / market_var
            alpha = stock_returns.mean() - beta * market_returns.mean()
            r_squared = correlation ** 2 if correlation is not None else 0.0
        
        return {
            'beta': float(beta),
            'correlation': float(correlation) if correlation is not None else None,
            'r_squared': float(r_squared),
            'alpha': float(alpha),
            'time_period': time_period
        }
    
    def calculate_rolling_volatility(self, stock_data: StockData, window: int = 30) -> Dict[str, Any]:
        """
        Calculate rolling volatility metrics
        
        Args:
            stock_data: Stock data
            window: Rolling window size in days
            
        Returns:
            Dictionary with rolling volatility statistics
        """
        import pandas as pd
        
        prices = stock_data.data['Close']
        returns = prices.pct_change().dropna()
        
        if len(returns) < window:
            return {
                'current_volatility': None,
                'avg_volatility': None,
                'volatility_trend': None,
                'window': window
            }
        
        # Calculate rolling volatility (annualized)
        rolling_vol = returns.rolling(window=window).std() * (252 ** 0.5)
        
        current_volatility = rolling_vol.iloc[-1] if not rolling_vol.empty else None
        avg_volatility = rolling_vol.mean() if not rolling_vol.empty else None
        
        # Volatility trend (comparing recent vs historical average)
        if len(rolling_vol) >= 2 and current_volatility is not None and avg_volatility is not None:
            volatility_trend = 'increasing' if current_volatility > avg_volatility else 'decreasing'
        else:
            volatility_trend = 'stable'
        
        return {
            'current_volatility': float(current_volatility) if current_volatility is not None else None,
            'average_volatility': float(avg_volatility) if avg_volatility is not None else None,
            'volatility_trend': volatility_trend,
            'volatility_series': rolling_vol,
            'window': window
        }
    
    def calculate_rolling_drawdown(self, stock_data: StockData, window: int = 30) -> Dict[str, Any]:
        """
        Calculate rolling drawdown metrics
        
        Args:
            stock_data: Stock data
            window: Rolling window size in days
            
        Returns:
            Dictionary with rolling drawdown statistics
        """
        import pandas as pd
        
        prices = stock_data.data['Close']
        
        if len(prices) < window:
            return {
                'current_drawdown': None,
                'max_rolling_drawdown': None,
                'avg_drawdown': None,
                'window': window
            }
        
        # Calculate rolling maximum drawdown
        rolling_max = prices.rolling(window=window).max()
        rolling_drawdown = (prices - rolling_max) / rolling_max
        
        current_drawdown = rolling_drawdown.iloc[-1] if not rolling_drawdown.empty else None
        max_rolling_drawdown = rolling_drawdown.min() if not rolling_drawdown.empty else None
        avg_drawdown = rolling_drawdown.mean() if not rolling_drawdown.empty else None
        
        # Calculate drawdown duration (days in current drawdown period)
        drawdown_duration = 0
        if not rolling_drawdown.empty:
            # Count consecutive days with negative drawdown from the end
            for i in reversed(range(len(rolling_drawdown))):
                if rolling_drawdown.iloc[i] < 0:
                    drawdown_duration += 1
                else:
                    break
        
        return {
            'current_drawdown': float(current_drawdown) if current_drawdown is not None else None,
            'max_drawdown': float(max_rolling_drawdown) if max_rolling_drawdown is not None else None,
            'average_drawdown': float(avg_drawdown) if avg_drawdown is not None else None,
            'drawdown_duration': drawdown_duration,
            'window': window
        }
    
    def assess_enhanced_risk(self, stock_data: StockData, market_data: StockData = None) -> Dict[str, Any]:
        """
        Comprehensive enhanced risk assessment
        
        Args:
            stock_data: Stock data
            market_data: Market index data (optional)
            
        Returns:
            Dictionary with all enhanced risk metrics
        """
        # Basic risk assessment
        basic_risk = self.assess_risk(stock_data)
        
        # Enhanced metrics
        var_95 = self.calculate_var(stock_data, confidence_level=0.95)
        var_99 = self.calculate_var(stock_data, confidence_level=0.99)
        sortino_ratio = self.calculate_sortino_ratio(stock_data)
        enhanced_beta = self.calculate_enhanced_beta(stock_data, market_data)
        rolling_volatility = self.calculate_rolling_volatility(stock_data)
        rolling_drawdown = self.calculate_rolling_drawdown(stock_data)
        
        # Enhanced risk classification
        enhanced_metrics = {
            **basic_risk,
            'var_95': var_95,
            'var_99': var_99,
            'sortino_ratio': sortino_ratio,
            'enhanced_beta': enhanced_beta,
            'rolling_volatility': rolling_volatility,
            'rolling_drawdown': rolling_drawdown
        }
        
        enhanced_risk_level = self.classify_enhanced_risk_level(enhanced_metrics)
        enhanced_metrics['enhanced_risk_level'] = enhanced_risk_level
        
        return enhanced_metrics
    
    def classify_enhanced_risk_level(self, risk_metrics: Dict[str, Any]) -> str:
        """
        Classify risk level based on enhanced metrics
        
        Args:
            risk_metrics: Dictionary of calculated risk metrics
            
        Returns:
            Risk level: 'VERY_LOW', 'LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH'
        """
        risk_score = 0
        
        # VaR contribution
        var_95 = risk_metrics.get('var_95', {}).get('var_percentage')
        if var_95 is not None:
            if var_95 < -0.05:  # More than 5% potential loss
                risk_score += 2
            elif var_95 < -0.03:  # 3-5% potential loss
                risk_score += 1
        
        # Volatility contribution
        volatility = risk_metrics.get('volatility')
        if volatility is None:
            # Fallback to daily volatility (annualize it)
            daily_vol = risk_metrics.get('daily_volatility')
            if daily_vol is not None:
                volatility = daily_vol * (252 ** 0.5)  # Annualize
        
        if volatility is not None:
            if volatility > 0.4:  # Very high volatility
                risk_score += 2
            elif volatility > 0.25:  # High volatility
                risk_score += 1
        
        # Beta contribution
        beta_info = risk_metrics.get('enhanced_beta', {})
        beta = beta_info.get('beta', 1.0)
        if beta > 1.5:  # High beta
            risk_score += 1
        elif beta > 2.0:  # Very high beta
            risk_score += 2
        
        # Sortino ratio contribution (lower is worse)
        sortino_ratio = risk_metrics.get('sortino_ratio')
        if sortino_ratio is not None:
            if sortino_ratio < -1.0:  # Very poor risk-adjusted returns
                risk_score += 2
            elif sortino_ratio < 0.0:  # Poor risk-adjusted returns
                risk_score += 1
        
        # Max drawdown contribution
        max_drawdown = risk_metrics.get('max_drawdown')
        if max_drawdown is not None:
            if abs(max_drawdown) > 0.3:  # More than 30% drawdown
                risk_score += 2
            elif abs(max_drawdown) > 0.2:  # 20-30% drawdown
                risk_score += 1
        
        # Add minimum risk for any real stock (no stock is truly risk-free)
        if risk_score == 0:
            # If all metrics are very good, still classify as LOW rather than VERY_LOW
            # since no stock is truly without risk
            risk_score = 2
        
        # Classify based on total risk score
        if risk_score >= 10:
            return 'VERY_HIGH'
        elif risk_score >= 7:
            return 'HIGH'
        elif risk_score >= 4:
            return 'MEDIUM'
        elif risk_score >= 2:
            return 'LOW'
        else:
            return 'VERY_LOW'
    
    def _classify_risk_level(self, risk_metrics: Dict[str, Any]) -> str:
        """Classify risk level as LOW/MEDIUM/HIGH"""
        daily_vol = risk_metrics.get('daily_volatility', 0.02)
        max_dd = abs(risk_metrics.get('max_drawdown', 0.1))
        
        if daily_vol < 0.015 and max_dd < 0.1:
            return 'LOW'
        elif daily_vol > 0.03 or max_dd > 0.2:
            return 'HIGH'
        else:
            return 'MEDIUM'
    
    def _calculate_sharpe_ratio(self, returns) -> float:
        """Calculate Sharpe ratio"""
        if returns.empty or returns.std() == 0:
            return 0.0
            
        # Assume risk-free rate of 3% annually (0.03/252 daily)
        risk_free_rate = 0.03 / 252
        excess_returns = returns.mean() - risk_free_rate
        
        return excess_returns / returns.std() * (252 ** 0.5)
    
    def _calculate_max_drawdown(self, prices) -> float:
        """Calculate maximum drawdown"""
        if prices.empty:
            return 0.0
            
        # Calculate running maximum
        running_max = prices.expanding().max()
        drawdown = (prices - running_max) / running_max
        
        return float(drawdown.min())
    
    def _calculate_beta(self, stock_returns, market_returns) -> float:
        """Calculate beta coefficient"""
        import numpy as np
        
        if len(stock_returns) != len(market_returns) or len(stock_returns) < 2:
            return 1.0  # Default beta
            
        # Calculate covariance and variance
        covariance = np.cov(stock_returns, market_returns)[0, 1]
        market_variance = np.var(market_returns)
        
        if market_variance == 0:
            return 1.0
            
        return covariance / market_variance