from typing import Dict, Any, Optional
from src.data.yahoo_client import StockData
from src.analysis.fundamental import FundamentalAnalysis

class FundamentalScorer:
    """Fundamental analysis scorer for enhanced recommendations"""
    
    def __init__(self):
        self.fundamental_analysis = FundamentalAnalysis()
    
    def calculate_score(self, stock_data: StockData) -> Dict[str, Any]:
        """Calculate fundamental analysis score"""
        try:
            # Get fundamental data
            fundamental_data = self.fundamental_analysis.get_fundamental_summary(stock_data)
            
            if not fundamental_data or all(v is None for v in fundamental_data.values()):
                return {
                    'score': None,
                    'details': {'data_quality': 'insufficient'},
                    'component_scores': {}
                }
            
            # Simple scoring for now - will enhance later
            pe_ratio = fundamental_data.get('pe_ratio')
            roe = fundamental_data.get('roe')
            debt_to_equity = fundamental_data.get('debt_to_equity')
            
            score = 0.0
            components = {}
            
            # P/E scoring (lower is better, but not too low)
            if pe_ratio:
                if 10 <= pe_ratio <= 25:
                    pe_score = 0.3
                elif pe_ratio < 10:
                    pe_score = 0.1  # Might be value trap
                else:
                    pe_score = -0.2  # Too expensive
                components['pe_score'] = pe_score
                score += pe_score
            
            # ROE scoring (higher is better)
            if roe:
                if roe > 15:
                    roe_score = 0.4
                elif roe > 10:
                    roe_score = 0.2
                else:
                    roe_score = -0.1
                components['roe_score'] = roe_score
                score += roe_score
            
            # Debt-to-equity scoring (lower is better)
            if debt_to_equity:
                if debt_to_equity < 0.3:
                    debt_score = 0.2
                elif debt_to_equity < 0.6:
                    debt_score = 0.0
                else:
                    debt_score = -0.3
                components['debt_score'] = debt_score
                score += debt_score
            
            # Normalize score
            score = max(-1.0, min(1.0, score))
            
            return {
                'score': score,
                'details': {
                    'fundamental_data': fundamental_data,
                    'data_quality': 'sufficient'
                },
                'component_scores': components
            }
            
        except Exception as e:
            return {
                'score': None,
                'details': {
                    'error': str(e),
                    'data_quality': 'insufficient'
                },
                'component_scores': {}
            }