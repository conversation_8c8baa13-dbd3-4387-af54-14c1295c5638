from typing import Dict, Any, Optional
from src.data.yahoo_client import StockData
from src.engine.decision_engine import RiskAssessment

class RiskScorer:
    """Risk analysis scorer for enhanced recommendations"""
    
    def __init__(self):
        self.risk_assessment = RiskAssessment()
    
    def calculate_score(self, stock_data: StockData, market_data: Optional[StockData] = None) -> Dict[str, Any]:
        """Calculate risk-adjusted score"""
        try:
            # Get enhanced risk assessment
            if market_data:
                risk_metrics = self.risk_assessment.assess_enhanced_risk(stock_data, market_data)
            else:
                risk_metrics = self.risk_assessment.assess_risk(stock_data)
            
            # Extract key risk metrics
            volatility = risk_metrics.get('volatility', 0.2)
            max_drawdown = risk_metrics.get('max_drawdown', -0.1)
            sharpe_ratio = risk_metrics.get('sharpe_ratio', 0)
            
            # VaR metrics if available
            var_95 = None
            if 'var_95' in risk_metrics:
                var_95 = risk_metrics['var_95'].get('var_percentage', 0)
            
            # Beta if available
            beta = 1.0
            if 'enhanced_beta' in risk_metrics:
                beta = risk_metrics['enhanced_beta'].get('beta', 1.0)
            
            # Calculate risk score (lower risk = higher score)
            score = 0.0
            
            # Volatility scoring (lower vol = better)
            if volatility < 0.2:
                vol_score = 0.3
            elif volatility < 0.3:
                vol_score = 0.1
            else:
                vol_score = -0.4
            score += vol_score
            
            # Drawdown scoring (smaller drawdown = better)
            if abs(max_drawdown) < 0.1:
                dd_score = 0.2
            elif abs(max_drawdown) < 0.2:
                dd_score = 0.0
            else:
                dd_score = -0.3
            score += dd_score
            
            # Sharpe ratio scoring
            if sharpe_ratio > 1.0:
                sharpe_score = 0.3
            elif sharpe_ratio > 0.5:
                sharpe_score = 0.1
            else:
                sharpe_score = -0.2
            score += sharpe_score
            
            # VaR scoring if available
            var_score = 0.0
            if var_95:
                if var_95 > -0.03:  # Low VaR
                    var_score = 0.2
                elif var_95 > -0.05:  # Medium VaR
                    var_score = 0.0
                else:  # High VaR
                    var_score = -0.3
                score += var_score
            
            # Normalize score
            score = max(-1.0, min(1.0, score))
            
            return {
                'score': score,
                'details': {
                    'volatility': volatility,
                    'max_drawdown': max_drawdown,
                    'sharpe_ratio': sharpe_ratio,
                    'var_95': var_95,
                    'beta': beta,
                    'risk_classification': risk_metrics.get('risk_level', 'MEDIUM')
                },
                'component_scores': {
                    'volatility_score': vol_score,
                    'drawdown_score': dd_score,
                    'sharpe_score': sharpe_score,
                    'var_score': var_score
                }
            }
            
        except Exception as e:
            return {
                'score': None,
                'details': {
                    'error': str(e),
                    'risk_classification': 'UNKNOWN'
                },
                'component_scores': {}
            }