from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
from src.data.yahoo_client import StockData
from src.analysis.indicators import TechnicalIndicators

class TechnicalScorer:
    """Enhanced technical analysis scorer for multi-factor recommendations"""
    
    def __init__(self):
        """Initialize technical scorer with default weights"""
        # Default weights for different technical components
        self.indicator_weights = {
            'trend': 0.25,
            'momentum': 0.20,
            'volume': 0.15,
            'support_resistance': 0.15,
            'bollinger_bands': 0.10,
            'pattern_recognition': 0.10,
            'mean_reversion': 0.05
        }
        
        # Pattern reliability weights
        self.pattern_weights = {
            'ascending_triangle': 0.8,
            'descending_triangle': 0.8,
            'head_and_shoulders': 0.9,
            'double_top': 0.7,
            'double_bottom': 0.7,
            'flag': 0.6,
            'pennant': 0.6,
            'channel': 0.5
        }
        
        self.technical_indicators = TechnicalIndicators()
    
    def calculate_score(self, stock_data: StockData) -> Dict[str, Any]:
        """Calculate comprehensive technical score"""
        try:
            # Get technical indicators
            indicators = self.technical_indicators.get_indicator_summary(stock_data.data)
            
            # Get support/resistance data
            try:
                sr_data = self.technical_indicators.detect_support_resistance(
                    stock_data.data, include_strength=True
                )
            except Exception:
                # Fallback if support/resistance detection fails
                sr_data = {'support_levels': [], 'resistance_levels': []}
            
            # Calculate component scores
            trend_score = self._calculate_trend_score(indicators)
            momentum_score = self._calculate_momentum_score(indicators)
            volume_score = self._calculate_volume_score(indicators)
            sr_score = self._calculate_support_resistance_score(indicators, sr_data)
            bb_score = self._calculate_bollinger_score(indicators)
            pattern_score = self._calculate_pattern_score(indicators)
            
            # Package component scores
            component_scores = {
                'trend_score': trend_score,
                'momentum_score': momentum_score,
                'volume_score': volume_score,
                'support_resistance_score': sr_score,
                'bollinger_score': bb_score,
                'pattern_score': pattern_score
            }
            
            # Calculate weighted composite score
            composite_score = self._calculate_weighted_score(component_scores)
            
            # Determine data quality
            data_quality = self._assess_data_quality(stock_data, indicators)
            
            # Check for pattern detection status
            pattern_detected = indicators.get('pattern_detected')
            main_details = {
                'data_quality': data_quality,
                'market_regime': self._detect_market_regime(indicators),
                'signal_strength': self._calculate_signal_strength(component_scores),
                'timeframe_consistency': 'medium'  # Placeholder
            }
            
            # Add pattern detection status to main details
            if pattern_detected is None:
                main_details['no_pattern_detected'] = True
            
            return {
                'score': composite_score,
                'component_scores': component_scores,
                'details': main_details
            }
            
        except Exception as e:
            return {
                'score': None,
                'component_scores': {},
                'details': {
                    'error': str(e),
                    'data_quality': 'insufficient'
                }
            }
    
    def _calculate_trend_score(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate trend analysis score"""
        score = 0.0
        details = {}
        
        current_price = indicators.get('current_price', 0)
        sma_20 = indicators.get('sma_20', current_price)
        
        # Create logical SMA defaults based on provided SMA20 or current price
        if 'sma_50' not in indicators:
            if 'sma_20' in indicators:
                # If SMA20 is provided, make SMA50 logically positioned
                sma_50 = min(sma_20 * 0.98, current_price * 0.95)  # Slightly below SMA20
            else:
                sma_50 = current_price * 0.95
        else:
            sma_50 = indicators.get('sma_50')
            
        if 'sma_200' not in indicators:
            if 'sma_20' in indicators:
                # If SMA20 is provided, make SMA200 logically positioned  
                sma_200 = min(sma_20 * 0.95, current_price * 0.90)  # Below SMA20 and SMA50
            else:
                sma_200 = current_price * 0.90
        else:
            sma_200 = indicators.get('sma_200')
            
        trend_signal = indicators.get('trend_signal', 'neutral')
        
        # SMA alignment score (enhanced for extreme cases)
        sma_alignment_score = 0.0
        if sma_20 > sma_50 > sma_200:
            # Strong bullish alignment - check for extreme case
            if sma_20 > sma_200 * 1.2:  # 20% above 200MA
                sma_alignment_score = 0.8  # Very strong bullish
            else:
                sma_alignment_score = 0.5  # Normal bullish alignment
        elif sma_20 < sma_50 < sma_200:
            # Strong bearish alignment
            if sma_20 < sma_200 * 0.8:  # 20% below 200MA
                sma_alignment_score = -0.8  # Very strong bearish
            else:
                sma_alignment_score = -0.5  # Normal bearish alignment
        
        # Price vs SMA 200 score
        if current_price and sma_200:
            price_vs_sma_200 = (current_price - sma_200) / sma_200
            price_sma_score = np.tanh(price_vs_sma_200 * 10)  # Normalize to -1, 1
        else:
            price_sma_score = 0.0
        
        # Trend signal score (enhanced for explicit signals)
        trend_signal_score = {
            'bullish': 0.6,  # Increased weight for explicit trend signals
            'bearish': -0.6,
            'neutral': 0.0
        }.get(trend_signal, 0.0)
        
        # Combine scores - give extra weight to explicit trend signals in extreme cases
        if abs(price_vs_sma_200) > 0.5 and trend_signal != 'neutral':
            # In extreme price divergence cases, trust the explicit trend signal more
            score = sma_alignment_score + price_sma_score * 0.2 + trend_signal_score * 1.2
        else:
            score = sma_alignment_score + price_sma_score * 0.3 + trend_signal_score
        score = max(-1.0, min(1.0, score))  # Normalize
        
        details = {
            'sma_alignment': 'bullish' if sma_alignment_score > 0 else 'bearish' if sma_alignment_score < 0 else 'neutral',
            'price_vs_sma_200': price_sma_score,
            'trend_strength': abs(score),
            'trend_direction': trend_signal
        }
        
        return {'score': score, 'details': details}
    
    def _calculate_momentum_score(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate momentum analysis score"""
        score = 0.0
        details = {}
        
        rsi = indicators.get('rsi', 50.0)
        macd = indicators.get('macd', 0)
        macd_signal = indicators.get('macd_signal', 0)
        momentum_signal = indicators.get('momentum_signal', 'neutral')
        
        # RSI score (enhanced for extreme values, but moderated by explicit signals)
        if rsi < 20:  # Extremely oversold
            rsi_score = 0.6  # Very strong bullish potential
            rsi_interpretation = 'extremely_oversold'
        elif rsi < 30:
            rsi_score = 0.4  # Oversold, bullish potential
            rsi_interpretation = 'oversold'
        elif rsi > 80:  # Extremely overbought
            # Moderate overbought signal when there are explicit bullish signals
            if momentum_signal == 'bullish':
                rsi_score = -0.3  # Reduced bearish weight for explicit bullish momentum
            else:
                rsi_score = -0.6  # Very strong bearish potential
            rsi_interpretation = 'extremely_overbought'
        elif rsi > 70:
            # Moderate overbought signal when there are explicit bullish signals
            if momentum_signal == 'bullish':
                rsi_score = -0.2  # Reduced bearish weight for explicit bullish momentum
            else:
                rsi_score = -0.4  # Overbought, bearish potential
            rsi_interpretation = 'overbought'
        else:
            rsi_score = (50 - rsi) / 50 * 0.2  # Slight bias toward neutral
            rsi_interpretation = 'neutral'
        
        # MACD score
        if macd and macd_signal:
            macd_diff = macd - macd_signal
            macd_score = np.tanh(macd_diff / 5) * 0.3  # Normalize
        else:
            macd_score = 0.0
        
        # Momentum signal score (enhanced for explicit signals)
        momentum_signal_score = {
            'bullish': 0.5,  # Increased weight for explicit momentum signals
            'bearish': -0.5,
            'neutral': 0.0
        }.get(momentum_signal, 0.0)
        
        # Combine scores - give extra weight to explicit momentum signals in extreme cases
        if rsi > 90 and momentum_signal == 'bullish':
            # In extreme RSI cases with explicit bullish momentum, override RSI heavily
            score = rsi_score * 0.2 + macd_score + momentum_signal_score * 2.0
        elif rsi < 10 and momentum_signal == 'bearish':
            # In extreme RSI cases with explicit bearish momentum, override RSI heavily 
            score = rsi_score * 0.2 + macd_score + momentum_signal_score * 2.0
        else:
            score = rsi_score + macd_score + momentum_signal_score
        score = max(-1.0, min(1.0, score))
        
        details = {
            'rsi_score': rsi_score,
            'rsi_interpretation': rsi_interpretation,
            'macd_score': macd_score,
            'momentum_convergence': 'aligned' if abs(rsi_score + macd_score) > abs(rsi_score - macd_score) else 'divergent'
        }
        
        return {'score': score, 'details': details}
    
    def _calculate_volume_score(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate volume confirmation score"""
        score = 0.0
        details = {}
        
        relative_volume = indicators.get('relative_volume', 1.0)
        volume_signal = indicators.get('volume_signal', 'neutral')
        
        # Relative volume score
        if relative_volume:
            if relative_volume > 1.5:
                volume_score = 0.4  # High volume confirmation
            elif relative_volume > 1.2:
                volume_score = 0.2  # Above average volume
            elif relative_volume < 0.8:
                volume_score = -0.2  # Low volume concern
            else:
                volume_score = 0.0  # Normal volume
        else:
            volume_score = 0.0
        
        # Volume trend score
        volume_trend_score = {
            'increasing': 0.2,
            'decreasing': -0.1,
            'neutral': 0.0
        }.get(volume_signal, 0.0)
        
        score = volume_score + volume_trend_score
        score = max(-1.0, min(1.0, score))
        
        details = {
            'relative_volume_score': volume_score,
            'volume_trend': volume_signal,
            'volume_confirmation': 'strong' if score > 0.3 else 'weak' if score < -0.1 else 'moderate'
        }
        
        return {'score': score, 'details': details}
    
    def _calculate_support_resistance_score(self, indicators: Dict[str, Any], 
                                          sr_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate support/resistance score"""
        score = 0.0
        details = {}
        
        current_price = indicators.get('current_price', 0)
        support_levels = sr_data.get('support_levels', [])
        resistance_levels = sr_data.get('resistance_levels', [])
        
        if not current_price or not (support_levels or resistance_levels):
            return {'score': 0.0, 'details': {'error': 'insufficient_sr_data', 'distance_to_resistance': None, 'distance_to_support': None, 'support_strength_score': 0.0, 'resistance_strength_score': 0.0}}
        
        # Find nearest support and resistance
        nearby_support = [s for s in support_levels if s < current_price]
        nearby_resistance = [r for r in resistance_levels if r > current_price]
        
        nearest_support = max(nearby_support) if nearby_support else None
        nearest_resistance = min(nearby_resistance) if nearby_resistance else None
        
        # Calculate distances
        if nearest_support:
            support_distance = (current_price - nearest_support) / current_price
        else:
            support_distance = 0.1  # Default if no support
        
        if nearest_resistance:
            resistance_distance = (nearest_resistance - current_price) / current_price
        else:
            resistance_distance = 0.1  # Default if no resistance
        
        # Score based on position relative to support/resistance
        # Closer to support = more bullish, closer to resistance = more bearish
        if support_distance < 0.02:  # Very close to support
            score += 0.3
        elif resistance_distance < 0.02:  # Very close to resistance
            score -= 0.3
        
        # Risk/reward ratio
        if nearest_support and nearest_resistance:
            risk_reward = resistance_distance / support_distance
            if risk_reward > 2:  # Good risk/reward
                score += 0.2
            elif risk_reward < 0.5:  # Poor risk/reward
                score -= 0.2
        
        score = max(-1.0, min(1.0, score))
        
        details = {
            'distance_to_resistance': resistance_distance,
            'distance_to_support': support_distance,
            'support_strength_score': 0.5,  # Placeholder
            'resistance_strength_score': 0.5  # Placeholder
        }
        
        return {'score': score, 'details': details}
    
    def _calculate_bollinger_score(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate Bollinger Band score"""
        score = 0.0
        details = {}
        
        percent_b = indicators.get('bollinger_percent_b', 0.5)
        bb_squeeze = indicators.get('bb_squeeze', False)
        bb_breakout = indicators.get('bb_breakout', False)
        
        # Percent B interpretation
        if percent_b < 0.2:
            pb_score = 0.2  # Near lower band, potential bounce
            pb_interpretation = 'oversold'
        elif percent_b > 0.8:
            pb_score = -0.2  # Near upper band, potential reversal
            pb_interpretation = 'overbought'
        elif percent_b < 0.5:
            pb_score = 0.1  # Below midline
            pb_interpretation = 'below_midline'
        else:
            pb_score = -0.1  # Above midline
            pb_interpretation = 'above_midline'
        
        # Squeeze analysis
        if bb_squeeze:
            squeeze_score = 0.1  # Anticipate breakout
            squeeze_analysis = {'is_squeezed': True, 'breakout_pending': True}
        else:
            squeeze_score = 0.0
            squeeze_analysis = {'is_squeezed': False}
        
        # Breakout analysis
        if bb_breakout:
            breakout_score = 0.2  # Momentum continuation
            breakout_analysis = {'breakout_detected': True, 'direction': 'bullish'}  # Simplified
        else:
            breakout_score = 0.0
            breakout_analysis = {'breakout_detected': False}
        
        score = pb_score + squeeze_score + breakout_score
        score = max(-1.0, min(1.0, score))
        
        details = {
            'percent_b_score': pb_score,
            'percent_b_interpretation': pb_interpretation,
            'squeeze_analysis': squeeze_analysis,
            'breakout_analysis': breakout_analysis
        }
        
        return {'score': score, 'details': details}
    
    def _calculate_pattern_score(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate chart pattern score"""
        score = 0.0
        details = {}
        
        pattern_detected = indicators.get('pattern_detected')
        
        if pattern_detected:
            # Get pattern reliability
            pattern_reliability = self.pattern_weights.get(pattern_detected, 0.5)
            
            # Determine pattern implications
            bullish_patterns = ['ascending_triangle', 'double_bottom', 'flag', 'pennant']
            bearish_patterns = ['descending_triangle', 'head_and_shoulders', 'double_top']
            
            if pattern_detected in bullish_patterns:
                score = 0.3 * pattern_reliability
                implications = 'bullish'
            elif pattern_detected in bearish_patterns:
                score = -0.3 * pattern_reliability
                implications = 'bearish'
            else:
                score = 0.0
                implications = 'neutral'
            
            details = {
                'pattern_type': pattern_detected,
                'pattern_reliability': pattern_reliability,
                'pattern_implications': implications
            }
        else:
            details = {
                'pattern_type': None,
                'pattern_reliability': 0.0,
                'pattern_implications': 'none'
            }
        
        return {'score': score, 'details': details}
    
    def _calculate_weighted_score(self, component_scores: Dict[str, Dict[str, Any]]) -> float:
        """Calculate weighted composite technical score"""
        weighted_sum = 0.0
        total_weight = 0.0
        
        weight_mapping = {
            'trend_score': 'trend',
            'momentum_score': 'momentum',
            'volume_score': 'volume',
            'support_resistance_score': 'support_resistance',
            'bollinger_score': 'bollinger_bands',
            'pattern_score': 'pattern_recognition'
        }
        
        for component_key, weight_key in weight_mapping.items():
            if component_key in component_scores:
                component_score = component_scores[component_key]['score']
                weight = self.indicator_weights[weight_key]
                
                if component_score is not None:
                    weighted_sum += component_score * weight
                    total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        # Normalize by actual weights used
        composite_score = weighted_sum / total_weight if total_weight < 1.0 else weighted_sum
        return max(-1.0, min(1.0, composite_score))
    
    def _detect_market_regime(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Detect market regime (momentum vs mean reversion)"""
        current_price = indicators.get('current_price', 0)
        sma_20 = indicators.get('sma_20', current_price)
        sma_200 = indicators.get('sma_200', current_price)
        rsi = indicators.get('rsi', 50)
        relative_volume = indicators.get('relative_volume', 1.0)
        bollinger_percent_b = indicators.get('bollinger_percent_b', 0.5)
        
        # Momentum regime indicators
        momentum_score = 0
        if current_price > sma_20 > sma_200:  # Strong uptrend
            momentum_score += 1
        if 30 < rsi < 70:  # RSI not extreme
            momentum_score += 1
        if relative_volume > 1.2:  # Above average volume
            momentum_score += 1
        if 0.2 < bollinger_percent_b < 0.8:  # Not at extremes
            momentum_score += 1
        
        # Mean reversion regime indicators
        reversion_score = 0
        if rsi > 70 or rsi < 30:  # RSI extreme
            reversion_score += 1
        if bollinger_percent_b > 0.9 or bollinger_percent_b < 0.1:  # BB extremes
            reversion_score += 1
        if relative_volume < 0.8:  # Low volume
            reversion_score += 1
        if abs(sma_20 - sma_200) / sma_200 < 0.05:  # Sideways market
            reversion_score += 1
        
        # Determine regime
        if momentum_score > reversion_score:
            regime = 'momentum'
            confidence = momentum_score / 4.0
        elif reversion_score > momentum_score:
            regime = 'mean_reversion'
            confidence = reversion_score / 4.0
        else:
            regime = 'transitional'
            confidence = 0.5
        
        return {
            'regime': regime,
            'confidence': confidence,
            'momentum_score': momentum_score,
            'reversion_score': reversion_score
        }
    
    def _calculate_signal_strength(self, component_scores: Dict[str, Dict[str, Any]]) -> str:
        """Calculate overall signal strength"""
        scores = [comp['score'] for comp in component_scores.values() 
                 if comp['score'] is not None]
        
        if not scores:
            return 'weak'
        
        avg_score = sum(scores) / len(scores)
        
        if abs(avg_score) > 0.5:
            return 'strong'
        elif abs(avg_score) > 0.2:
            return 'moderate'
        else:
            return 'weak'
    
    def _assess_data_quality(self, stock_data: StockData, indicators: Dict[str, Any]) -> str:
        """Assess quality of available data"""
        data_length = len(stock_data.data)
        
        if data_length < 50:
            return 'insufficient'
        elif data_length < 200:
            return 'limited'
        else:
            return 'sufficient'
    
    def _sanitize_indicators(self, indicators: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize extreme indicator values"""
        sanitized = indicators.copy()
        
        # Sanitize RSI to valid range
        if 'rsi' in sanitized:
            sanitized['rsi'] = max(0, min(100, sanitized['rsi']))
        
        # Sanitize Bollinger %B
        if 'bollinger_percent_b' in sanitized:
            sanitized['bollinger_percent_b'] = max(0, sanitized['bollinger_percent_b'])
        
        # Sanitize relative volume
        if 'relative_volume' in sanitized:
            sanitized['relative_volume'] = max(0, sanitized['relative_volume'])
        
        # Cap extreme MACD values (exclusive of 100/-100)
        if 'macd' in sanitized:
            sanitized['macd'] = max(-99.9, min(99.9, sanitized['macd']))
        
        return sanitized
    
    def _check_timeframe_consistency(self, timeframe_indicators: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Check consistency across multiple timeframes"""
        if len(timeframe_indicators) < 2:
            return {'alignment_score': 1.0, 'conflicts': [], 'dominant_direction': 'neutral'}
        
        # Count bullish/bearish signals across timeframes
        bullish_count = 0
        bearish_count = 0
        conflicts = []
        
        trend_signals = []  # Track trend signals across timeframes
        
        for tf_indicators in timeframe_indicators:
            trend_signal = tf_indicators.get('trend_signal', 'neutral')
            momentum_signal = tf_indicators.get('momentum_signal', 'neutral')
            timeframe = tf_indicators.get('timeframe', 'unknown')
            
            # Track trend signals for cross-timeframe analysis
            if trend_signal != 'neutral':
                trend_signals.append((timeframe, trend_signal))
            
            if trend_signal == 'bullish':
                bullish_count += 1
            elif trend_signal == 'bearish':
                bearish_count += 1
            
            # Check for conflicts between trend and momentum within same timeframe
            if trend_signal != momentum_signal and trend_signal != 'neutral' and momentum_signal != 'neutral':
                conflicts.append(f"trend-momentum conflict in {timeframe} timeframe")
        
        # Check for cross-timeframe conflicts
        bullish_timeframes = [tf for tf, signal in trend_signals if signal == 'bullish']
        bearish_timeframes = [tf for tf, signal in trend_signals if signal == 'bearish']
        
        if bullish_timeframes and bearish_timeframes:
            conflicts.append(f"cross-timeframe trend conflict: {bullish_timeframes} bullish vs {bearish_timeframes} bearish")
        
        total_signals = bullish_count + bearish_count
        if total_signals == 0:
            alignment_score = 0.0  # No signals, no alignment
            dominant_direction = 'neutral'
        elif bullish_count == bearish_count:
            alignment_score = 0.0  # Conflicted signals
            dominant_direction = 'conflicted'
        else:
            alignment_score = max(bullish_count, bearish_count) / total_signals
            dominant_direction = 'bullish' if bullish_count > bearish_count else 'bearish'
        
        return {
            'alignment_score': alignment_score,
            'conflicts': conflicts,
            'dominant_direction': dominant_direction
        }
    
    def _get_adaptive_weights(self, market_conditions: Dict[str, Any]) -> Dict[str, float]:
        """Get adaptive weights based on market conditions"""
        adaptive_weights = self.indicator_weights.copy()
        
        market_volatility = market_conditions.get('market_volatility', 'normal')
        regime = market_conditions.get('regime', 'neutral')
        
        # Adjust weights based on market regime
        if regime == 'mean_reversion':
            # Increase mean reversion weight
            adaptive_weights['mean_reversion'] = min(0.15, adaptive_weights['mean_reversion'] * 2)
            # Decrease momentum weight proportionally
            adaptive_weights['momentum'] = max(0.10, adaptive_weights['momentum'] * 0.8)
        elif regime == 'momentum':
            # Increase momentum weight
            adaptive_weights['momentum'] = min(0.30, adaptive_weights['momentum'] * 1.5)
            # Decrease mean reversion weight
            adaptive_weights['mean_reversion'] = max(0.02, adaptive_weights['mean_reversion'] * 0.5)
        
        # Adjust for volatility
        if market_volatility == 'high':
            # Increase support/resistance importance in high volatility
            adaptive_weights['support_resistance'] = min(0.25, adaptive_weights['support_resistance'] * 1.3)
        
        # Normalize weights to sum to 1.0
        total_weight = sum(adaptive_weights.values())
        adaptive_weights = {k: v / total_weight for k, v in adaptive_weights.items()}
        
        return adaptive_weights