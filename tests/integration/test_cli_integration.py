import pytest
import json
import tempfile
import os
from unittest.mock import patch, Mock
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.cli.commands import cli

class TestCLIIntegration:
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    @pytest.fixture
    def mock_complete_analysis(self, sample_analysis_result):
        """Mock complete analysis pipeline"""
        def mock_pipeline(symbol, period='1y', detailed=False, risk='moderate'):
            result = sample_analysis_result.copy()
            result['symbol'] = symbol
            result['period'] = period
            result['detailed'] = detailed
            result['risk_tolerance'] = risk
            return result
        return mock_pipeline
    
    def test_end_to_end_basic_analysis(self, runner, mock_complete_analysis):
        """Test complete end-to-end basic stock analysis"""
        with patch('src.cli.commands.fetch_and_analyze', side_effect=mock_complete_analysis):
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO'])
            
            assert result.exit_code == 0
            assert 'RELIANCE.BO' in result.output
            assert 'BUY' in result.output
            assert 'Confidence: 78%' in result.output
    
    def test_end_to_end_detailed_analysis(self, runner, mock_complete_analysis):
        """Test complete end-to-end detailed analysis"""
        with patch('src.cli.commands.fetch_and_analyze', side_effect=mock_complete_analysis):
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO', '--detailed'])
            
            assert result.exit_code == 0
            assert 'RELIANCE.BO' in result.output
            assert 'RSI' in result.output
            assert 'Bollinger Upper' in result.output or 'Relative Volume' in result.output
    
    def test_end_to_end_json_output(self, runner, mock_complete_analysis):
        """Test end-to-end JSON output"""
        with patch('src.cli.commands.fetch_and_analyze', side_effect=mock_complete_analysis):
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO', '--format', 'json'])
            
            assert result.exit_code == 0
            
            # Should be valid JSON
            output_data = json.loads(result.output)
            assert output_data['symbol'] == 'RELIANCE.BO'
            assert 'recommendation' in output_data
            assert 'technical_indicators' in output_data
    
    def test_end_to_end_csv_output(self, runner, mock_complete_analysis):
        """Test end-to-end CSV output"""
        with patch('src.cli.commands.fetch_and_analyze', side_effect=mock_complete_analysis):
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO', '--format', 'csv'])
            
            assert result.exit_code == 0
            
            lines = result.output.strip().split('\n')
            assert len(lines) >= 2  # Header + data
            assert 'symbol' in lines[0]  # Header
            assert 'RELIANCE.BO' in lines[1]  # Data
    
    def test_end_to_end_with_all_options(self, runner, mock_complete_analysis):
        """Test end-to-end with all command options"""
        with patch('src.cli.commands.fetch_and_analyze', side_effect=mock_complete_analysis):
            result = runner.invoke(cli, [
                'analyze', 'RELIANCE.BO',
                '--period', '2y',
                '--risk', 'aggressive', 
                '--detailed',
                '--format', 'json'
            ])
            
            assert result.exit_code == 0
            
            # Verify JSON output with all options
            output_data = json.loads(result.output)
            assert output_data['symbol'] == 'RELIANCE.BO'
            assert 'recommendation' in output_data
            assert 'technical_indicators' in output_data

class TestRealDataIntegration:
    """Integration tests with real components (no mocking of core logic)"""
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    def test_invalid_symbol_error_flow(self, runner):
        """Test complete error flow for invalid symbol"""
        result = runner.invoke(cli, ['analyze', 'INVALID'])
        
        assert result.exit_code != 0
        assert 'Error' in result.output
        assert 'Invalid' in result.output or 'symbol' in result.output
    
    def test_network_error_handling(self, runner):
        """Test network error handling in complete flow"""
        with patch('src.data.yahoo_client.YahooClient.fetch_stock_data') as mock_fetch:
            mock_fetch.side_effect = ConnectionError("Network connection failed")
            
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO'])
            
            assert result.exit_code != 0
            assert 'Error' in result.output
            assert 'connection' in result.output.lower() or 'network' in result.output.lower()
    
    def test_api_timeout_handling(self, runner):
        """Test API timeout handling"""
        with patch('src.data.yahoo_client.YahooClient.fetch_stock_data') as mock_fetch:
            mock_fetch.side_effect = TimeoutError("Request timed out")
            
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO'])
            
            assert result.exit_code != 0
            assert 'Error' in result.output
            assert 'timeout' in result.output.lower() or 'time' in result.output.lower()

class TestFileOperations:
    """Test file operations that are implemented"""
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    @pytest.fixture
    def temp_output_dir(self):
        with tempfile.TemporaryDirectory() as temp_dir:
            yield temp_dir
    
    def test_csv_export_to_file(self, runner, temp_output_dir, sample_analysis_result):
        """Test CSV export to file"""
        output_file = os.path.join(temp_output_dir, 'analysis.csv')
        
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = sample_analysis_result
            
            result = runner.invoke(cli, [
                'analyze', 'RELIANCE.BO',
                '--format', 'csv',
                '--output', output_file
            ])
            
            assert result.exit_code == 0
            assert os.path.exists(output_file)
            
            # Verify file content
            with open(output_file, 'r') as f:
                content = f.read()
                assert 'RELIANCE.BO' in content
                assert 'symbol' in content  # Header
    
    def test_json_export_to_file(self, runner, temp_output_dir, sample_analysis_result):
        """Test JSON export to file"""
        output_file = os.path.join(temp_output_dir, 'analysis.json')
        
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = sample_analysis_result
            
            result = runner.invoke(cli, [
                'analyze', 'RELIANCE.BO',
                '--format', 'json',
                '--output', output_file
            ])
            
            assert result.exit_code == 0
            assert os.path.exists(output_file)
            
            # Verify JSON file content
            with open(output_file, 'r') as f:
                data = json.load(f)
                assert data['symbol'] == 'RELIANCE.BO'

class TestPerformance:
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    def test_analysis_performance_timing(self, runner):
        """Test that analysis completes within reasonable time"""
        import time
        
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            # Simulate realistic processing time
            def slow_analysis(symbol, *args, **kwargs):
                time.sleep(0.1)  # 100ms processing time
                return {'symbol': symbol, 'recommendation': 'BUY'}
            
            mock_analyze.side_effect = slow_analysis
            
            start_time = time.time()
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO'])
            end_time = time.time()
            
            assert result.exit_code == 0
            assert (end_time - start_time) < 5.0  # Should complete in < 5 seconds

class TestUserExperience:
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    def test_help_accessibility(self, runner):
        """Test help information accessibility"""
        # Main help
        result = runner.invoke(cli, ['--help'])
        assert result.exit_code == 0
        assert 'Usage:' in result.output
        assert 'Commands:' in result.output
        
        # Command-specific help
        result = runner.invoke(cli, ['analyze', '--help'])
        assert result.exit_code == 0
        assert 'SYMBOL' in result.output
        assert '--period' in result.output
    
    def test_error_message_clarity(self, runner):
        """Test error message clarity and helpfulness"""
        # Missing required argument
        result = runner.invoke(cli, ['analyze'])
        assert result.exit_code != 0
        assert 'Usage:' in result.output or 'Missing' in result.output
        
        # Invalid option value
        result = runner.invoke(cli, ['analyze', 'RELIANCE.BO', '--period', 'invalid'])
        assert result.exit_code != 0
        assert 'invalid' in result.output.lower() or 'period' in result.output
    
    def test_progress_indicators(self, runner):
        """Test progress indicators for long operations"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            def slow_analysis(symbol, **kwargs):
                # Simulate slow operation
                import time
                time.sleep(0.2)
                return {'symbol': symbol, 'recommendation': 'BUY'}
            
            mock_analyze.side_effect = slow_analysis
            
            result = runner.invoke(cli, ['analyze', 'RELIANCE.BO'])
            
            assert result.exit_code == 0
            # Should have some indication of processing
            assert 'Analyzing' in result.output or 'Processing' in result.output or len(result.output) > 0
    
    def test_output_formatting_consistency(self, runner):
        """Test output formatting consistency across different scenarios"""
        test_cases = [
            (['analyze', 'RELIANCE.BO'], 'table'),
            (['analyze', 'TCS.BO', '--format', 'json'], 'json'),
            (['analyze', 'INFY.BO', '--format', 'csv'], 'csv')
        ]
        
        for command_args, expected_format in test_cases:
            with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
                mock_analyze.return_value = {'symbol': 'TEST.BO', 'recommendation': 'BUY'}
                
                result = runner.invoke(cli, command_args)
                
                assert result.exit_code == 0
                assert len(result.output.strip()) > 0
                
                # Format-specific checks
                if expected_format == 'json':
                    assert result.output.strip().startswith('{')
                elif expected_format == 'csv':
                    assert ',' in result.output
                elif expected_format == 'table':
                    assert 'BUY' in result.output