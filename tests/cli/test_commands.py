import pytest
from unittest.mock import Mock, patch
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.cli.commands import analyze_stock, cli
from src.data.yahoo_client import StockData

class TestStockAnalyzeCommand:
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    def test_basic_stock_analysis(self, runner, sample_stock_data):
        """Test basic stock analysis command"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = {
                'symbol': 'RELIANCE.BO',
                'recommendation': 'BUY',
                'confidence': 0.78
            }
            
            result = runner.invoke(analyze_stock, ['RELIANCE.BO'])
            
            assert result.exit_code == 0
            assert 'RELIANCE.BO' in result.output
            mock_analyze.assert_called_once_with('RELIANCE.BO', period='1y', detailed=False, risk='moderate')
    
    def test_stock_analysis_with_period(self, runner):
        """Test stock analysis with custom period"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = {'symbol': 'TCS.BO', 'recommendation': 'HOLD'}
            
            result = runner.invoke(analyze_stock, ['TCS.BO', '--period', '2y'])
            
            assert result.exit_code == 0
            mock_analyze.assert_called_once_with('TCS.BO', period='2y', detailed=False, risk='moderate')
    
    def test_detailed_analysis(self, runner):
        """Test detailed analysis flag"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = {'symbol': 'INFY.BO', 'recommendation': 'SELL'}
            
            result = runner.invoke(analyze_stock, ['INFY.BO', '--detailed'])
            
            assert result.exit_code == 0
            mock_analyze.assert_called_once_with('INFY.BO', period='1y', detailed=True, risk='moderate')
    
    def test_json_format_output(self, runner, expected_json_output):
        """Test JSON format output"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = expected_json_output
            
            result = runner.invoke(analyze_stock, ['RELIANCE.BO', '--format', 'json'])
            
            assert result.exit_code == 0
            assert '"symbol": "RELIANCE.BO"' in result.output
            assert '"recommendation"' in result.output
    
    def test_csv_format_output(self, runner):
        """Test CSV format output"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = {
                'symbol': 'HDFC.BO',
                'current_price': 1500.0,
                'recommendation': 'BUY'
            }
            
            result = runner.invoke(analyze_stock, ['HDFC.BO', '--format', 'csv'])
            
            assert result.exit_code == 0
            assert 'symbol,current_price,recommendation' in result.output
            assert 'HDFC.BO,1500.0,BUY' in result.output
    
    def test_risk_tolerance_parameter(self, runner):
        """Test risk tolerance parameter"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = {'symbol': 'WIPRO.BO', 'recommendation': 'BUY'}
            
            result = runner.invoke(analyze_stock, ['WIPRO.BO', '--risk', 'aggressive'])
            
            assert result.exit_code == 0
            # Should pass risk parameter to analysis function
            mock_analyze.assert_called_once_with('WIPRO.BO', period='1y', detailed=False, risk='aggressive')
    
    def test_invalid_stock_symbol(self, runner):
        """Test handling of invalid stock symbols"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.side_effect = ValueError("Invalid BSE symbol format: INVALID")
            
            result = runner.invoke(analyze_stock, ['INVALID'])
            
            assert result.exit_code != 0
            assert 'Error' in result.output
            assert 'Invalid BSE symbol format' in result.output
    
    def test_invalid_period(self, runner):
        """Test handling of invalid period"""
        result = runner.invoke(analyze_stock, ['RELIANCE.BO', '--period', 'invalid'])
        
        assert result.exit_code != 0
        assert 'Invalid value for \'--period\'' in result.output
    
    def test_invalid_risk_tolerance(self, runner):
        """Test handling of invalid risk tolerance"""
        result = runner.invoke(analyze_stock, ['RELIANCE.BO', '--risk', 'invalid'])
        
        assert result.exit_code != 0
        assert 'Invalid value for \'--risk\'' in result.output
    
    def test_network_error_handling(self, runner):
        """Test handling of network errors"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.side_effect = ConnectionError("Failed to fetch data: Network error")
            
            result = runner.invoke(analyze_stock, ['RELIANCE.BO'])
            
            assert result.exit_code != 0
            assert 'Network error' in result.output
            assert 'Failed to fetch data' in result.output

class TestCLIInterface:
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    def test_cli_help(self, runner):
        """Test CLI help message"""
        result = runner.invoke(cli, ['--help'])
        
        assert result.exit_code == 0
        assert 'BSE Stock Analysis Tool' in result.output
        assert 'Commands:' in result.output
        assert 'analyze' in result.output
    
    def test_analyze_command_help(self, runner):
        """Test analyze command help"""
        result = runner.invoke(cli, ['analyze', '--help'])
        
        assert result.exit_code == 0
        assert 'Analyze a BSE stock' in result.output
        assert '--period' in result.output
        assert '--format' in result.output
        assert '--detailed' in result.output
        assert '--risk' in result.output
    
    def test_version_command(self, runner):
        """Test version command"""
        result = runner.invoke(cli, ['--version'])
        
        assert result.exit_code == 0
        assert '0.1.0' in result.output  # Version from setup.py

class TestParameterValidation:
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    def test_valid_periods(self, runner):
        """Test all valid period values"""
        valid_periods = ['1m', '3m', '6m', '1y', '2y']
        
        for period in valid_periods:
            with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
                mock_analyze.return_value = {'symbol': 'TEST.BO', 'recommendation': 'HOLD'}
                
                result = runner.invoke(analyze_stock, ['TEST.BO', '--period', period])
                assert result.exit_code == 0
    
    def test_valid_risk_tolerances(self, runner):
        """Test all valid risk tolerance values"""
        valid_risks = ['conservative', 'moderate', 'aggressive']
        
        for risk in valid_risks:
            with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
                mock_analyze.return_value = {'symbol': 'TEST.BO', 'recommendation': 'HOLD'}
                
                result = runner.invoke(analyze_stock, ['TEST.BO', '--risk', risk])
                assert result.exit_code == 0
    
    def test_valid_output_formats(self, runner):
        """Test all valid output formats"""
        valid_formats = ['table', 'json', 'csv']
        
        for format_type in valid_formats:
            with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
                mock_analyze.return_value = {'symbol': 'TEST.BO', 'recommendation': 'HOLD'}
                
                result = runner.invoke(analyze_stock, ['TEST.BO', '--format', format_type])
                assert result.exit_code == 0
    
    def test_symbol_parameter_required(self, runner):
        """Test that symbol parameter is required"""
        result = runner.invoke(analyze_stock, [])
        
        assert result.exit_code != 0
        assert 'Missing argument' in result.output or 'Usage:' in result.output
    
    def test_case_insensitive_symbol(self, runner):
        """Test case insensitive symbol handling"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = {'symbol': 'RELIANCE.BO', 'recommendation': 'BUY'}
            
            # Should accept both upper and lower case
            result1 = runner.invoke(analyze_stock, ['reliance.bo'])
            result2 = runner.invoke(analyze_stock, ['RELIANCE.BO'])
            
            assert result1.exit_code == 0
            assert result2.exit_code == 0
            
            # Should normalize to uppercase
            calls = mock_analyze.call_args_list
            assert calls[0][0][0] == 'RELIANCE.BO'  # Normalized to uppercase
            assert calls[1][0][0] == 'RELIANCE.BO'

class TestCommandIntegration:
    
    @pytest.fixture
    def runner(self):
        return CliRunner()
    
    def test_full_command_chain(self, runner, sample_analysis_result):
        """Test complete command execution chain"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.return_value = sample_analysis_result
            
            result = runner.invoke(analyze_stock, [
                'RELIANCE.BO', 
                '--period', '1y', 
                '--risk', 'moderate', 
                '--format', 'table',
                '--detailed'
            ])
            
            assert result.exit_code == 0
            assert 'BUY' in result.output
            assert 'Confidence: 78%' in result.output
            mock_analyze.assert_called_once_with(
                'RELIANCE.BO', 
                period='1y', 
                detailed=True, 
                risk='moderate'
            )
    
    def test_error_propagation(self, runner):
        """Test that errors are properly propagated and formatted"""
        with patch('src.cli.commands.fetch_and_analyze') as mock_analyze:
            mock_analyze.side_effect = Exception("Unexpected error occurred")
            
            result = runner.invoke(analyze_stock, ['RELIANCE.BO'])
            
            assert result.exit_code != 0
            assert 'Error' in result.output
            assert 'Unexpected error occurred' in result.output